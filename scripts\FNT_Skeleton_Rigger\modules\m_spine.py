import maya.cmds as cmds
import math

from . import shape, utility, moduleList

LockAttributes = [".translateX", ".translateZ"]

Type = moduleList.ModuleData["spine"]["Name"]
BoneNumber = moduleList.ModuleData["spine"]["BoneNumber"]

BaseName = Type + "_proxy_base_"
EndName = Type + "_proxy_end_"




def build(Parent = None, Bones = None):

    MainController = utility.create_controller_to_bone(Bones[0], shape.box, shape.Yellow, ParentGroup=Parent, CustomName="Main")

    PelvisController = utility.create_controller_to_bone(Bones[0], shape.circle, shape.Yellow, ParentGroup=MainController)

    SpineControllers = utility.create_controller_to_bone(Bones[1:], shape.circle, shape.Yellow, ParentGroup=MainController)


    utility.constrain_bones_to_controller(Bones[0], PelvisController)
    utility.constrain_bones_to_controller(Bones[1:], SpineControllers) 


    