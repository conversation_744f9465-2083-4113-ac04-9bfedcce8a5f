import os

import maya.cmds as cmds
from maya import OpenMayaUI as omui
from maya.app.general.mayaMixin import MayaQWidgetDockableMixin

from PySide2 import QtCore, QtGui, QtWidgets
from PySide2.QtCore import *
from PySide2.QtGui import *
from PySide2.QtWidgets import *

import importlib

from . import version
from .modules import ui, shape, utility, moduleList, bones_func

List = list(moduleList.ModuleData.keys())



class UISetup_AutoRig(MayaQWidgetDockableMixin, QtWidgets.QDialog):

    def __init__(self, parent=None):
        super(UISetup_AutoRig, self).__init__(parent)

        self.initUI()



    def initUI(self):

        self.setWindowTitle(version.Name)
        self.setGeometry(100, 100, 300, 600)


        # Create Main Layout and Main Widget
        self.mainLayout = QVBoxLayout()
        self.TabWidget = QTabWidget()


        self.tab_bones()
        self.tab_shape()

        # Create window 
        self.mainLayout.addWidget(self.TabWidget)
        self.setLayout(self.mainLayout)


    def tab_bones(self):

        BonesMain = QWidget()
        BonesMainLayout = QVBoxLayout(BonesMain)

        Widget_BoneRenamer = QWidget()
        Widget_Build = QWidget()
        Widget_ShapeEditor = QWidget()
        Widget_Modules = QWidget()

        BonesMainLayout.addWidget(ui.SeparatorWidget())
        ui.title_label(BonesMainLayout, "Bone Renamer")
        BonesMainLayout.addWidget(Widget_BoneRenamer)

        BonesMainLayout.addSpacing(10)
        BonesMainLayout.addWidget(ui.SeparatorWidget())
        BonesMainLayout.addStretch()

        self.zone_bone_renamer(Widget_BoneRenamer)

        self.TabWidget.addTab(BonesMain, "Bones")

    def tab_shape(self):
        
        ShapeMain = QWidget()
        ShapeMainLayout = QVBoxLayout(ShapeMain)

        Widget_Test = QWidget()
        Widget_Build = QWidget()
        Widget_ShapeEditor = QWidget()
        Widget_Modules = QWidget()


        ShapeMainLayout.addWidget(ui.SeparatorWidget())
        ui.title_label(ShapeMainLayout, "Test Zone")
        ShapeMainLayout.addWidget(Widget_Test)
        ShapeMainLayout.addWidget(ui.SeparatorWidget())
        ui.title_label(ShapeMainLayout, "Build")
        ShapeMainLayout.addWidget(Widget_Build)
        ShapeMainLayout.addWidget(ui.SeparatorWidget())
        ui.title_label(ShapeMainLayout, "Shape editor")
        ShapeMainLayout.addWidget(Widget_ShapeEditor)
        ShapeMainLayout.addWidget(ui.SeparatorWidget())
        ui.title_label(ShapeMainLayout, "Modules")
        ShapeMainLayout.addWidget(Widget_Modules)
        ShapeMainLayout.addSpacing(50)
        ShapeMainLayout.addWidget(ui.SeparatorWidget())
        ShapeMainLayout.addStretch()

        self.zone_test(Widget_Test)
        self.zone_build(Widget_Build)
        self.zone_shape_editor(Widget_ShapeEditor)
        self.zone_modules(Widget_Modules)

        self.TabWidget.addTab(ShapeMain, "Shape")


    def zone_bone_renamer(self, Widget = None):
        LayoutV_Main = QVBoxLayout(Widget)

        LayoutH_Name = QHBoxLayout()
        TF_Prefix = QLineEdit()
        TF_Prefix.setPlaceholderText("Prefix")
        TF_Name = QLineEdit()
        TF_Name.setPlaceholderText("Name")
        TF_Suffix = QLineEdit()
        TF_Suffix.setPlaceholderText("Suffix")
        LayoutH_Name.addWidget(TF_Prefix)
        LayoutH_Name.addWidget(TF_Name)
        LayoutH_Name.addWidget(TF_Suffix)

        if version.DebugRenamer:
            TF_Prefix.setText("Bo_")
            TF_Name.setText("Ergo")
            TF_Suffix.setText("_L")

        LayoutV_Main.addLayout(LayoutH_Name)
        ui.create_button("Rename", LayoutV_Main, lambda:bones_func.rename_bones(TF_Prefix.text(), TF_Name.text(), TF_Suffix.text()))

        

    def zone_test(self, Widget = None):
        Layout_TestZone = QHBoxLayout(Widget)

        ui.create_button("Delete ctrl", Layout_TestZone, self.delete_root_ctrl)
        ui.create_button("Test02", Layout_TestZone, lambda:utility.duplicate_bones_hierarchy())

    def zone_build(self, Widget = None):
        MainLayout_Build = QVBoxLayout(Widget)

        Layout_Build = QHBoxLayout()
        MainLayout_Build.addLayout(Layout_Build)
        Layout_Option = QHBoxLayout()
        MainLayout_Build.addLayout(Layout_Option)

        ui.create_button("Create", Layout_Build, self.create_rig)
        ui.create_button("Create (with parent)", Layout_Build, lambda:self.create_rig(WithParent=True))

        self.Spinbox_Number = QSpinBox()
        self.Spinbox_Number.setValue(1)
        self.Spinbox_Number.setMinimum(0)
        Layout_Option.addWidget(self.Spinbox_Number)

        self.TextField_Prefix = QLineEdit()
        self.TextField_Prefix.setPlaceholderText("Custom name")
        self.TextField_Prefix.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        Layout_Option.addWidget(self.TextField_Prefix)

    def zone_shape_editor(self, Widget = None):
        MainLayout_ShapeEditor = QVBoxLayout(Widget)

        Layout_Scale = QHBoxLayout()
        MainLayout_ShapeEditor.addLayout(Layout_Scale)

        ui.title_label(Layout_Scale, "Scale:")
        ui.create_button("----", Layout_Scale, lambda:utility.scale_shape(cmds.ls(selection=True), 0.5))
        ui.create_button("-", Layout_Scale, lambda:utility.scale_shape(cmds.ls(selection=True), 0.75))
        ui.create_button("+", Layout_Scale, lambda:utility.scale_shape(cmds.ls(selection=True), 1.5))
        ui.create_button("++++", Layout_Scale, lambda:utility.scale_shape(cmds.ls(selection=True), 2))

    def zone_modules(self, Widget = None):
        MainLayout_Modules = QVBoxLayout(Widget)
        self.Module_List = QListWidget()
        self.Module_List.addItems(List)
        MainLayout_Modules.addWidget(self.Module_List)




    def delete_root_ctrl(self):
        cmds.delete("CTRL")

    def test02(self):
        select = cmds.ls(selection=True)
        utility.space_switch_float(select[1], select[0])

################################################################

    def create_rig(self, WithParent=False):

        try:
            Item = self.Module_List.selectedItems()[0]
            Item = Item.text()
        except:
            print("WARNING: Select a module")
            return
            
        FileName = moduleList.ModuleData[str(Item)]["FileName"]
        Module = importlib.import_module("FNT_Skeleton_Rigger.modules." + FileName)

        Selection = cmds.ls(selection=True)

        if WithParent:
            Module.build(Parent = Selection[0], Bones = Selection[1:])
        else:
            Module.build(Bones = Selection)

