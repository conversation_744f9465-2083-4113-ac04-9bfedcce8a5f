import maya.cmds as cmds

Red = (1,0,0)
Green = (0,1,0)
Blue = (0,0,1)

Yellow = (1,1,0)
YellowBrown = (1,0.5,0)

def color_override(Obj, Color):
    cmds.setAttr(Obj + ".overrideEnabled", 1)
    cmds.setAttr(Obj + ".overrideRGBColors", 1)
    cmds.setAttr(Obj + ".overrideColorRGB", *Color)

###############################################

def circle(Name = "Circle", Color = Yellow, Scale=1):
    Current = cmds.circle(n = Name, radius = 10, nr = [1,0,0])
    color_override(Current[0], Color)

    cmds.scale(Scale,Scale,Scale, str(Current[0]))
    cmds.makeIdentity(a = True)

    return Current

def circle_up(Name = "Circle Up", Color = Yellow, Scale=1):
    Current = cmds.circle(n = Name, radius = 10, nr = [0,1,0])
    color_override(Current[0], Color)

    cmds.scale(Scale,Scale,Scale, str(Current[0]))
    cmds.makeIdentity(a = True)


    return Current



def square(Name = "Square", Color = Yellow, Scale=1):
    Current = cmds.curve(n = Name, d = 1, p = [(1,0,-1), (1,0,1), (-1,0,1), (-1,0,-1), (1,0,-1)])
    color_override(Current, Color)
    cmds.scale(10,10,10, str(Current))
    cmds.makeIdentity(a = True)
    return Current


def diamond(Name = "Diamond", Color = Yellow, Scale=1):
    Current = cmds.circle(n = Name, radius = 10, nr = [0,1,0], d = 1, s = 4)
    color_override(Current[0], Color)
    return Current


def pentagone(Name = "Pentagone", Color = Yellow, Scale=1):
    Current = cmds.circle(n = Name, radius = 10, nr = [0,1,0], d = 1, s = 5)
    color_override(Current[0], Color)
    return Current


def arrow(Name = "Arrow", Color = Yellow, Scale=1):
    Current = cmds.curve(n = Name, d = 1, p = [(1,0,0), (0.25,0,0.75), (0.25,0,0.25), (-1,0,0.25), (-1,0,-0.25), (0.25,0,-0.25), (0.25,0,-0.75), (1,0,0)])
    color_override(Current, Color)
    cmds.scale(10,10,10, str(Current))
    cmds.rotate(0,-90,0, str(Current))
    cmds.makeIdentity(a = True)
    return Current


def cube(Name = "Cube", Color = Yellow, Scale=1):
    Current = cmds.curve(n = Name, d = 1, p = [(1,1,-1), (1,1,1), (-1,1,1), (-1,1,-1), (1,1,-1), (1,-1,-1), 
                                               (1,-1,1), (1,1,1), (1,-1,1), 
                                               (-1,-1,1), (-1,1,1), (-1,-1,1),
                                               (-1,-1,-1), (-1,1,-1), (-1,-1,-1),
                                               (1,-1,-1)])
    color_override(Current, Color)
    cmds.scale(10,10,10, str(Current))
    cmds.makeIdentity(a = True)
    return Current

def box(Name = "Cube", Color = Yellow, Scale=1):
    Current = cmds.curve(n = Name, d = 1, p = [(1,1,-1), (1,1,1), (-1,1,1), (-1,1,-1), (1,1,-1), (1,-1,-1), 
                                               (1,-1,1), (1,1,1), (1,-1,1), 
                                               (-1,-1,1), (-1,1,1), (-1,-1,1),
                                               (-1,-1,-1), (-1,1,-1), (-1,-1,-1),
                                               (1,-1,-1)])
    color_override(Current, Color)
    cmds.scale(15,5,15, str(Current))
    cmds.makeIdentity(a = True)
    return Current


def house(Name = "House", Color = Yellow, Scale = 1):
    Current = cmds.curve(n = Name, d = 1, p = [(0.75,0,-1), (1,0,0), (0.75,0,1), (-1,0,1), (-1,0,-1), (0.75,0,-1)])
    color_override(Current, Color)

    cmds.scale(10,10,10, str(Current))
    cmds.rotate(0,-90,0, str(Current))
    cmds.makeIdentity(a = True)

    cmds.scale(Scale,Scale,Scale, str(Current))
    cmds.makeIdentity(a = True)

    return Current


def sphere(Name = "Sphere", Color = Yellow, Scale=1):
    
    Current = cmds.circle(n = Name, radius = 10, nr = [0,1,0])
    Temp02 = cmds.circle(n = Name + "02", radius = 10, nr = [1,0,0])
    Temp03 = cmds.circle(n = Name + "03", radius = 10, nr = [0,0,1])

    cmds.parent(cmds.listRelatives(Temp02, shapes = True), Current[0], r=True, s=True)
    cmds.parent(cmds.listRelatives(Temp03, shapes = True), Current[0], r=True, s=True)
    cmds.delete(Temp02, Temp03)

    color_override(Current[0], Color)
    return Current


def gear(Name = "Gear", Color = Yellow, Scale=1):
    Current = cmds.curve(n = Name, d = 1, p = [(0.25,0,-0.25), (1,0,-0.25), (1,0,0.25), (0.25,0,0.25),
                                                (0.25,0,1), (-0.25,0,1), (-0.25,0,0.25), (-1,0,0.25),
                                                (-1,0,-0.25), (-0.25,0,-0.25), (-0.25,0,-1), (0.25,0,-1),
                                                (0.25,0,-0.25)])
    color_override(Current, Color)
    cmds.scale(10,10,10, str(Current))
    cmds.rotate(0,45,0, str(Current))
    cmds.makeIdentity(a = True)
    return Current


def dropplet(Name = "Dropplet", Color = Yellow, Scale=1):
    Current = cmds.curve(n = Name, d = 3, p = [(1,0,0), (0,0,-0.75),(-1,0,0),(0,0,0.75),(1,0,0)])
    Temp = cmds.curve(n = Name + "02", d = 3, p = [(1,0,0), (0,0,-0.75),(-1,0,0),(0,0,0.75),(1,0,0)])

    cmds.rotate(90,0,0, str(Temp))
    cmds.makeIdentity(Temp, a = True)

    cmds.parent(cmds.listRelatives(Temp, shapes = True), Current, r=True, s=True)
    cmds.delete(Temp)

    color_override(Current, Color)
    cmds.scale(5,5,5, str(Current))
    cmds.rotate(0,0,-90, str(Current))
    
    cmds.makeIdentity(Current, a = True)
    return Current


def proxy(Name = "Proxy", Color = Yellow, Scale = 1):
    Current = cube(Name=Name)
    Temp = cmds.curve(n = Name + "02", d = 1, p = [(2,0,0), (-2,0,0),
                                                   (0,0,0),(0,0,2),(0,0,-2),
                                                   (0,0,0),(0,2,0),(0,-2,0)])
    cmds.scale(10,10,10, Temp)
    cmds.makeIdentity(Temp, a=True)

    cmds.parent(cmds.listRelatives(Temp, shapes = True), Current, r=True, s=True)
    cmds.delete(Temp)

    cmds.scale(Scale,Scale,Scale, Current)
    cmds.makeIdentity(Current, a=True)

    color_override(Current, Color)
    return Current