import maya.cmds as cmds
import maya.api.OpenMaya as om
from . import shape
from .. import version

BaseNameCtrl = "empty"
BaseNameGroup = "Ctrl_Offset"
BaseColor = (1,1,0)

Ctrl_GroupName = "CTRL"
Handle_GroupName = "HANDLE"
FunctionBones_GroupName = "FUNCTION_BONES"

##########################
# Master group functions
##########################

def check_ctrl_group():
    Name = Ctrl_GroupName

    if cmds.objExists(Name):
        pass
    else:
        cmds.group(em=True, n=Name)

    return Name

def check_handle_group():
    Name = Handle_GroupName

    if cmds.objExists(Name):
        pass
    else:
        obj = cmds.group(em=True, n=Name)
        cmds.parent(obj, "CTRL")

    if not version.DebugShowAllHierarchy:
        cmds.setAttr(f"{Name}.visibility", 0)

    return Name

def check_function_bones_group():
    Name = FunctionBones_GroupName

    if cmds.objExists(Name):
        pass
    else:
        obj = cmds.group(em=True, n=Name)
        cmds.parent(obj, Ctrl_GroupName)

    if not version.DebugShowAllHierarchy:
        cmds.setAttr(f"{Name}.visibility", 0)

    return Name

##########################
# Small steps functions
##########################

def offset_group(ShapeType, Name=BaseNameCtrl, Color=BaseColor):
    Current = ShapeType(Name + "_ctrl", Color)
    Group = cmds.group(Current, n=Name + "_offset")
    return Current, Group

def match_transform_to_joint(Target, Joint, IsParent=False):     
    if IsParent:      
        Rotation = [cmds.getAttr(str(Joint) + ".jointOrientX"),
                cmds.getAttr(str(Joint) + ".jointOrientY"),
                cmds.getAttr(str(Joint) + ".jointOrientZ"),
                ]
    else:
        Rotation = [0,0,0]

    cmds.matchTransform(Target, Joint, scl=False)

def parent_offset_group_to_other(ControllerArray, Parent):

    for obj in ControllerArray:
        OffsetGroup = cmds.listRelatives(obj, parent=True, type='transform')
        cmds.parent(OffsetGroup, Parent)

def constrain_offset_group_to_other(ControllerArray, Parent, Offset = False):

    for obj in ControllerArray:
        OffsetGroup = cmds.listRelatives(obj, parent=True, type='transform')
        cmds.parentConstraint(Parent, OffsetGroup, mo=Offset)

def hide_main_attributes(Selection, Translation = True, Rotation = True, Scale = True, Visibility = True):
    if Translation:
        cmds.setAttr(f"{Selection}.tx", k=False, cb=False, l=True)
        cmds.setAttr(f"{Selection}.ty", k=False, cb=False, l=True)
        cmds.setAttr(f"{Selection}.tz", k=False, cb=False, l=True)
    if Rotation:
        cmds.setAttr(f"{Selection}.rx", k=False, cb=False, l=True)
        cmds.setAttr(f"{Selection}.ry", k=False, cb=False, l=True)
        cmds.setAttr(f"{Selection}.rz", k=False, cb=False, l=True)
    if Scale:
        cmds.setAttr(f"{Selection}.sx", k=False, cb=False, l=True)
        cmds.setAttr(f"{Selection}.sy", k=False, cb=False, l=True)
        cmds.setAttr(f"{Selection}.sz", k=False, cb=False, l=True)
    if Visibility:
        cmds.setAttr(f"{Selection}.v", k=False, cb=False, l=True)

##########################
# Controllers / bones relationship functions
##########################

def create_controller_to_bone(BoneArray, ShapeType, Color=BaseColor, ParentGroup=None, CustomName=None, ShapeSize=1, Suffix=""):
    ControllerArray = []
    PreviousShape = None
    FilteredArray = []

    if isinstance(BoneArray, list):
        FilteredArray.extend(BoneArray)
    else:
        FilteredArray.append(BoneArray)



    for Bone in FilteredArray:
        if CustomName:
            Current = ShapeType(CustomName + Suffix + "_ctrl", Color, Scale=ShapeSize)
            Group = cmds.group(Current, n=CustomName + Suffix + "_offset" + Suffix)
        else:
            Current = ShapeType(str(Bone) + Suffix + "_ctrl", Color, Scale=ShapeSize)
            Group = cmds.group(Current, n=str(Bone) + Suffix + "_offset")
        
        if PreviousShape:
            cmds.parent(Group, PreviousShape)
            cmds.xform()
            match_transform_to_joint(Group, Bone)
        else:
            if ParentGroup is None:
                CTRLGroup = check_ctrl_group()
                cmds.parent(Group, CTRLGroup)
            else:
                cmds.parent(Group, ParentGroup)

            match_transform_to_joint(Group, Bone, IsParent=True)
        
        PreviousShape = Current
        
        if isinstance(Current, list):
            ControllerArray.append(Current[0])
        else:
            ControllerArray.append(Current)
        
            
    return ControllerArray

def create_controller_custom_transform(BoneArray, Transform, ShapeType, Color=BaseColor, ParentGroup=None, CustomName=None):
    ControllerArray = []
    PreviousShape = None
    FilteredArray = []

    if isinstance(BoneArray, list):
        FilteredArray.extend(BoneArray)
    else:
        FilteredArray.append(BoneArray)



    for Bone in FilteredArray:
        if CustomName:
            Current = ShapeType(CustomName + "_ctrl", Color)
            Group = cmds.group(Current, n=CustomName + "_offset")
        else:
            Current = ShapeType(str(Bone) + "_ctrl", Color)
            Group = cmds.group(Current, n=str(Bone) + "_offset")
        
        if PreviousShape:
            cmds.parent(Group, PreviousShape)
            cmds.matchTransform(Group, Bone)
            cmds.xform(Group, t=Transform)
        else:
            if ParentGroup is None:
                CTRLGroup = check_ctrl_group()
                cmds.parent(Group, CTRLGroup)
            else:
                cmds.parent(Group, ParentGroup)

            cmds.matchTransform(Group, Bone)
            cmds.xform(Group, t=Transform)
        
        PreviousShape = Current
        
        if isinstance(Current, list):
            ControllerArray.append(Current[0])
        else:
            ControllerArray.append(Current)
        
            
    return ControllerArray

def constrain_bones_to_controller(BoneArray, ControllerArray, Offset=False):

    for index, ctrl in enumerate(ControllerArray):

        if isinstance(BoneArray, list):
            bone = BoneArray[index]
        else:
            bone = BoneArray


        cmds.parentConstraint(ctrl, bone, mo=Offset)

##########################
# IK functions
##########################

def create_ik_handle(RootBone, PoleBone, TargetBone, ParentHandleToIk = True):

    check_ctrl_group()
    HandleGroup = check_handle_group()

    Handle = cmds.ikHandle(n= TargetBone + "_ikh", sj=RootBone, ee=TargetBone)
    cmds.parent(Handle[0], HandleGroup)

    IkCtrl = create_controller_to_bone(TargetBone, shape.cube, Color=shape.Blue, CustomName=TargetBone + "_ik")

    if ParentHandleToIk:
        cmds.parentConstraint(IkCtrl[0], Handle[0], mo=False)

    cmds.orientConstraint(IkCtrl[0], TargetBone, mo=True)

    # Pole vector maths
    start = cmds.xform(RootBone, q=True, ws=True, t=True)
    mid = cmds.xform(PoleBone, q=True, ws=True, t=True)
    end = cmds.xform(TargetBone, q=True, ws=True, t=True)

    startV = om.MVector(start[0], start[1], start[2])
    midV = om.MVector(mid[0], mid[1], mid[2])
    endV = om.MVector(end[0], end[1], end[2])

    startEnd = endV - startV
    startMid = midV - startV
    dotP = startMid * startEnd
    proj = float(dotP) / float(startEnd.length())
    startEndN = startEnd.normal()
    projV = startEndN * proj
    arrowV = startMid - projV
    arrowV *= 20
    finalV = arrowV + midV

    PoleVectorCtrl = create_controller_custom_transform(PoleBone, [finalV.x, finalV.y, finalV.z], shape.dropplet, Color=shape.Blue, CustomName=TargetBone + "_pv")
    cmds.poleVectorConstraint(PoleVectorCtrl[0], Handle[0])
    print([finalV.x, finalV.y, finalV.z])

    # Parent Ik to position ctrl if exist
    PositionCtrl = "position_ctrl"
    if cmds.objExists(PositionCtrl):
        parent_offset_group_to_other(IkCtrl, PositionCtrl)
        parent_offset_group_to_other(PoleVectorCtrl, PositionCtrl)

    return IkCtrl[0], PoleVectorCtrl[0], Handle[0]

def duplicate_bones_hierarchy(BoneArray = None, Suffix = "ik"):

    NewBoneArray = []

    if BoneArray == None:
        BoneArray = cmds.ls(selection=True)

    check_function_bones_group()

    for x, bone in enumerate(BoneArray):
        New = cmds.duplicate(bone, n=f"{bone}_{Suffix}", po=True)

        NewBoneArray.extend(New)

        if x == 0:
            cmds.parent(New, FunctionBones_GroupName)
        else:
            cmds.parent(New, NewBoneArray[x-1])

    return NewBoneArray



##########################
# Space switch functions
##########################

def space_switch_enum(Controller = None, Spaces = None, Translation = False):
    # WIP

    # Create group and contraint to all spaces
    cmds.select(Controller)
    SpacesArray = []
    if isinstance(Spaces, list):
        SpacesArray.extend(Spaces)
    else:
        SpacesArray.append(Spaces)

    SpaceGroup = cmds.group(n=f"{Controller}Spaces")

    for obj in SpacesArray:
        if Translation:
            cmds.parentConstraint(obj, SpaceGroup, mo=True)
        else:
            cmds.orientConstraint(obj, SpaceGroup)

    # Add space switch attributs with all spaces
    StringSpaceArray = ":".join(str(obj) for obj in SpacesArray)
    cmds.addAttr(Controller, sn="Spaces", at="enum", en=StringSpaceArray, k=True)

def space_switch_float(Controller = None, control_attr = None, constraint = False):

    # 1. Create a reverse node to invert the control attribute value (0 -> 1 and 1 -> 0)
    reverse_node = cmds.createNode("reverse", name="reverse_node")
    cmds.connectAttr(f"{Controller}.{control_attr}", f"{reverse_node}.inputX")

    # 2. Create two multiplyDivide nodes: one for inputA and one for inputB
    multiplyA_node = cmds.createNode("multiplyDivide", name="multiplyA_node")
    multiplyB_node = cmds.createNode("multiplyDivide", name="multiplyB_node")

    # 3. Set multiplyDivide nodes to multiply by 1 (to maintain the same value)
    cmds.setAttr(f"{multiplyA_node}.operation", 1)  # Multiply
    cmds.setAttr(f"{multiplyB_node}.operation", 1)  # Multiply

    # 4. Connect the custom attribute (control_attr) to inputA node and reverse to inputB node
    cmds.connectAttr(f"{Controller}.{control_attr}", f"{multiplyA_node}.input1X")  # inputA uses the original control_attr
    cmds.connectAttr(f"{reverse_node}.outputX", f"{multiplyB_node}.input1X")  # inputB uses the reversed value

    # 5. Now, connect the result to the constraint inputs
    ConstrainAt0 = cmds.listAttr(constraint, k=True, st="*W0*")[0]
    ConstrainAt1 = cmds.listAttr(constraint, k=True, st="*W1*")[0]
    cmds.connectAttr(f"{multiplyA_node}.outputX", f"{constraint}.{ConstrainAt0}")  # Connect to inputA
    cmds.connectAttr(f"{multiplyB_node}.outputX", f"{constraint}.{ConstrainAt1}")  # Connect to inputB

##########################
# Shape editing functions
##########################

def scale_shape(Selection = [], Factor=1.5):

    for obj in Selection:
        cvs = []

        Curves = cmds.listRelatives(obj, s=True, typ="nurbsCurve")

        for curve in Curves:
            cvs.extend(cmds.ls(f"{curve}.cv[*]", flatten=True))

        cmds.select(cvs)

        cmds.scale(Factor,Factor,Factor, ocp=True, r=True)

        cmds.select(Selection)


def visibility_from_float(ControllerArray = [], SettingController = "", FloatAttribute = "", Value0 = True):
    
    for Control in ControllerArray:

        if Value0:
            reverse_node = cmds.createNode("reverse", name=f"{Control}reverse_node")
            cmds.connectAttr(f"{SettingController}.{FloatAttribute}", f"{reverse_node}.inputX")
            cmds.connectAttr(f"{reverse_node}.outputX", f"{Control}.visibility")

        else:
            cmds.connectAttr(f"{SettingController}.{FloatAttribute}", f"{Control}.visibility")