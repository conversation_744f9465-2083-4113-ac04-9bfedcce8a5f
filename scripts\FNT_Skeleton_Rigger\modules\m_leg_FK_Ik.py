import maya.cmds as cmds
import maya.mel as mel
import math

from . import shape, utility, moduleList




def build(Parent = None, Bones = None, Side = None):

    # Setup settings ctrl
    SettingCtrl = utility.create_controller_to_bone(Bones[-2], shape.gear, shape.Blue, 
                                                    ParentGroup=Parent, 
                                                    CustomName= str(Bones[-2]) + "_Setting")
    utility.constrain_offset_group_to_other(SettingCtrl, Bones[-2])
    utility.hide_main_attributes(SettingCtrl[0])

    # Setup ik system
    IkBonesArray = utility.duplicate_bones_hierarchy([Bones[0], Bones[1], Bones[-2]], Suffix="ik")
    IkCtrlArray = utility.create_ik_handle(IkBonesArray[0], IkBonesArray[1], IkBonesArray[-1], False)
    IkHandle = IkCtrlArray[-1]
    IkCtrlArray = IkCtrlArray[:-1]
    cmds.parentConstraint(Parent, IkBonesArray[0], mo=True)

    # Setup fk system
    FkBonesArray = utility.duplicate_bones_hierarchy([Bones[0], Bones[1], Bones[-2]], Suffix="fk")
    FkCtrlArray = utility.create_controller_to_bone(FkBonesArray, shape.circle, shape.Blue, ParentGroup=Parent, Suffix="_fk")
    utility.constrain_bones_to_controller(FkBonesArray, FkCtrlArray)

    # Constrain main bones to fk and ik bones
    utility.constrain_bones_to_controller(Bones, IkBonesArray)
    utility.constrain_bones_to_controller(Bones, FkBonesArray)

    # Ik fk switch on settings ctrl
    SwitchAttributeName = "FkIk"
    cmds.addAttr(SettingCtrl, ln=SwitchAttributeName, at= "float", min=0, max=1, k=True)
    
    # Connect switch to constrains
    for bone in Bones[:-1]:
        Constraint = cmds.listRelatives(bone, type="constraint", parent=False)[0]

        utility.space_switch_float(SettingCtrl[0], SwitchAttributeName, Constraint)

    # Controllers visibility from float
    utility.visibility_from_float(FkCtrlArray, SettingCtrl[0], "FkIk")
    utility.visibility_from_float(IkCtrlArray, SettingCtrl[0], "FkIk", Value0=False)
    
    ##########################
    # Ball setup
    ##########################

    # Create the visible ball controller
    BallCtrl = utility.create_controller_to_bone(Bones[-1], shape.circle, shape.Blue)
    utility.constrain_bones_to_controller(Bones[-1], BallCtrl)
    utility.constrain_offset_group_to_other(BallCtrl, IkCtrlArray[-2], True)
    utility.constrain_offset_group_to_other(BallCtrl, FkCtrlArray[-1], True)

    BallConstraint = cmds.listRelatives(cmds.listRelatives(BallCtrl, parent=True, type='transform'), type="constraint", parent=False)[0]
    utility.space_switch_float(SettingCtrl[0], SwitchAttributeName, BallConstraint)

    # Create the invisible controller for the ball slider
    BallSystemCtrl = utility.create_controller_to_bone(Bones[-1], shape.diamond, shape.Yellow, CustomName=Bones[-1] + "_system", ShapeSize=0.1)
    cmds.setAttr(f"{BallSystemCtrl[0]}.visibility", 0)

    # Constrain for the ctrl, the bones and the handle
    utility.constrain_bones_to_controller(cmds.listRelatives(BallSystemCtrl, parent=True, type='transform'), [IkCtrlArray[0]], Offset=True)
    utility.constrain_bones_to_controller(IkHandle, BallSystemCtrl, Offset=True)

    cmds.delete(cmds.listRelatives(IkBonesArray[-1], typ="constraint"))
    cmds.orientConstraint(BallSystemCtrl, IkBonesArray[-1], mo=True)

    # Slider
    SliderName = "BallRoll"
    cmds.addAttr(IkCtrlArray[0], ln=SliderName, min=0, at= "float", k=True)

    reverse_node = cmds.createNode("reverse", name=f"{BallSystemCtrl[0]}_reverse_node")
    cmds.connectAttr(f"{IkCtrlArray[0]}.{SliderName}", f"{reverse_node}.inputX")
    cmds.connectAttr(f"{reverse_node}.outputX", f"{BallSystemCtrl[0]}.rotateZ")

