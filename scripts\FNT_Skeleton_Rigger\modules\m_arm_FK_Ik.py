import maya.cmds as cmds
import math

from . import shape, utility, moduleList




def build(Parent = None, Bones = None, Side = None):

    # Setup elbow ctrl
    ShoulderCtrl = utility.create_controller_to_bone(Bones[0], shape.circle, shape.Blue, ParentGroup=Parent)
    utility.constrain_bones_to_controller(Bones[0], ShoulderCtrl)

    # Setup settings ctrl
    SettingCtrl = utility.create_controller_to_bone(Bones[-1], shape.gear, shape.Blue, 
                                                    ParentGroup=Parent, 
                                                    CustomName= str(Bones[-1]) + "_Setting")
    utility.constrain_offset_group_to_other(SettingCtrl, Bones[-1])
    utility.hide_main_attributes(SettingCtrl[0])

    # Setup ik system
    IkBonesArray = utility.duplicate_bones_hierarchy([Bones[1], Bones[2], Bones[-1]], Suffix="ik")
    IkCtrlArray = utility.create_ik_handle(IkBonesArray[0], IkBonesArray[1], IkBonesArray[-1])
    cmds.parentConstraint(Bones[0], IkBonesArray[0], mo=True)

    # Setup fk system
    FkBonesArray = utility.duplicate_bones_hierarchy([Bones[1], Bones[2], Bones[-1]], Suffix="fk")
    FkCtrlArray = utility.create_controller_to_bone(FkBonesArray, shape.circle, shape.Blue, ParentGroup=ShoulderCtrl, Suffix="_fk")
    utility.constrain_bones_to_controller(FkBonesArray, FkCtrlArray)

    # Constrain main bones to fk and ik bones
    utility.constrain_bones_to_controller(Bones[1:], IkBonesArray)
    utility.constrain_bones_to_controller(Bones[1:], FkBonesArray)

    # Ik fk switch on settings ctrl
    SwitchAttributeName = "FkIk"
    cmds.addAttr(SettingCtrl, ln=SwitchAttributeName, at= "float", min=0, max=1, k=True)
    
    # Connect switch to constrains
    for bone in Bones[1:]:
        Constraint = cmds.listRelatives(bone, type="constraint", parent=False)[0]

        utility.space_switch_float(SettingCtrl[0], SwitchAttributeName, Constraint)

    # Controllers visibility from float
    utility.visibility_from_float(FkCtrlArray, SettingCtrl[0], "FkIk")
    utility.visibility_from_float(IkCtrlArray, SettingCtrl[0], "FkIk", Value0=False)
    