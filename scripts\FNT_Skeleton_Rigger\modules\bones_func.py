import maya.cmds as cmds
import maya.api.OpenMaya as om

def rename_bones(Prefix = "", Name = "", Suffix = ""):

    Selection = cmds.ls(selection=True)

    for num, obj in enumerate(Selection):
        Number = num + 1

        if num < 9:
            Number = f"_0{Number}"
            print("Less than 10")

        if num >= 9:
            Number = f"_{Number}"
            print("More than 10")

        if len(Selection) == 1:
            Number = ""
            print("One selectio")

        cmds.rename(obj, f"{Prefix}{Name}{Suffix}{Number}")
