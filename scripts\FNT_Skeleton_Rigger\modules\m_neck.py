import maya.cmds as cmds
import math

from . import shape, utility, moduleList



def build(Parent = None, <PERSON> = None):


    ControllerArray = utility.create_controller_to_bone(Bones[:-1], shape.circle, ParentGroup=Parent)
    HeadController = utility.create_controller_to_bone(Bones[-1], shape.sphere)
    utility.parent_offset_group_to_other(<PERSON><PERSON><PERSON><PERSON><PERSON>, ControllerArray[-1])

    ControllerArray.append(HeadController[0])

    utility.constrain_bones_to_controller(<PERSON>, ControllerArray)




    