import maya.cmds as cmds

from . import shape, utility, moduleList




def build(Parent = None, <PERSON> = None):

    if Parent:
        Bones = Parent

    MainCtrl = utility.create_controller_to_bone(Bones, shape.house, shape.Green, CustomName="master", ShapeSize=8)
    MainOffsetCtrl = utility.create_controller_to_bone(Bones, shape.circle_up, shape.Yellow, MainCtrl, CustomName="position", ShapeSize=6)
    RootCtrl = utility.create_controller_to_bone(Bones, shape.arrow, shape.Green, MainOffsetCtrl)

    utility.constrain_bones_to_controller(Bones, RootCtrl) 


    